'use client';

import React, { useState, useCallback, useEffect } from 'react';
import { useToast } from '@/hooks/use-toast';
import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Calendar } from '@/components/ui/calendar';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Loader2, CalendarIcon, Zap, CheckCircle, Receipt } from 'lucide-react';
import { format } from 'date-fns';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Label } from '@/components/ui/label';
import { Skeleton } from '@/components/ui/skeleton';

// Progressive form data interface
interface ProgressiveExpenditureFormData {
  date: Date;
  category: string;
  subcategory?: string;
  amount: string;
  reference: string;
  description?: string;
  fiscalYear: string;
  status: string;
  paymentMethod?: string;
  vendor?: string;
  department?: string;
  costCenter?: string;
  budget: string;
  budgetCategory: string;
  budgetSubcategory?: string;
  appliedToBudget?: boolean;
  notes?: string;
}

interface ProgressiveExpenditureFormProps {
  expenditure?: any;
  onSubmit: (data: any) => Promise<void>;
  onCancel: () => void;
  isLoading?: boolean;
}

// Static data that loads instantly (no backend dependency)
const INSTANT_FORM_DATA = {
  expenseCategories: [
    { value: 'office_supplies', label: 'Office Supplies' },
    { value: 'travel_transport', label: 'Travel & Transport' },
    { value: 'utilities', label: 'Utilities' },
    { value: 'professional_services', label: 'Professional Services' },
    { value: 'equipment', label: 'Equipment' },
    { value: 'maintenance', label: 'Maintenance' },
    { value: 'training', label: 'Training & Development' },
    { value: 'communications', label: 'Communications' },
    { value: 'insurance', label: 'Insurance' },
    { value: 'personnel', label: 'Personnel' },
    { value: 'administrative', label: 'Administrative' },
    { value: 'other', label: 'Other' },
  ],
  fiscalYears: [
    { value: '2024-2025', label: '2024-2025 (Current)' },
    { value: '2023-2024', label: '2023-2024' },
    { value: '2025-2026', label: '2025-2026' },
  ],
  statusOptions: [
    { value: 'draft', label: 'Draft' },
    { value: 'pending_approval', label: 'Pending Approval' },
    { value: 'approved', label: 'Approved' },
    { value: 'paid', label: 'Paid' },
  ],
  paymentMethods: [
    { value: 'bank_transfer', label: 'Bank Transfer' },
    { value: 'check', label: 'Check' },
    { value: 'cash', label: 'Cash' },
    { value: 'credit_card', label: 'Credit Card' },
    { value: 'mobile_money', label: 'Mobile Money' },
  ],
  departments: [
    { value: 'administration', label: 'Administration' },
    { value: 'finance', label: 'Finance' },
    { value: 'hr', label: 'Human Resources' },
    { value: 'it', label: 'Information Technology' },
    { value: 'operations', label: 'Operations' },
    { value: 'general', label: 'General' },
  ],
};

// Progressive data loader for budget data (mirrors income pattern)
class ProgressiveDataLoader {
  private static instance: ProgressiveDataLoader;
  private budgets: any[] = [];
  private budgetCategories: any[] = [];
  private isLoading = false;
  private isLoaded = false;

  static getInstance(): ProgressiveDataLoader {
    if (!ProgressiveDataLoader.instance) {
      ProgressiveDataLoader.instance = new ProgressiveDataLoader();
    }
    return ProgressiveDataLoader.instance;
  }

  async loadBudgetData(): Promise<{ budgets: any[]; budgetCategories: any[] }> {
    if (this.isLoaded) {
      return { budgets: this.budgets, budgetCategories: this.budgetCategories };
    }

    if (this.isLoading) {
      // Wait for existing load to complete
      while (this.isLoading) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
      return { budgets: this.budgets, budgetCategories: this.budgetCategories };
    }

    this.isLoading = true;

    try {
      // Check cache first
      const cacheTimestamp = localStorage.getItem('expenditure-form-cache-timestamp');
      const cacheAge = cacheTimestamp ? Date.now() - parseInt(cacheTimestamp) : Infinity;
      const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

      if (cacheAge < CACHE_DURATION) {
        const cachedBudgets = localStorage.getItem('expenditure-form-budgets');
        const cachedCategories = localStorage.getItem('expenditure-form-budget-categories');
        
        if (cachedBudgets && cachedCategories) {
          this.budgets = JSON.parse(cachedBudgets);
          this.budgetCategories = JSON.parse(cachedCategories);
          this.isLoaded = true;
          return { budgets: this.budgets, budgetCategories: this.budgetCategories };
        }
      }

      // Fetch budget data
      const [budgetsResponse, categoriesResponse] = await Promise.all([
        fetch('/api/accounting/budget?status=active'),
        fetch('/api/accounting/budget/categories?type=expense')
      ]);

      const budgetsData = budgetsResponse.ok ? await budgetsResponse.json() : { budgets: [] };
      const categoriesData = categoriesResponse.ok ? await categoriesResponse.json() : { categories: [] };

      // Process and normalize data
      const processedBudgets = (budgetsData.budgets || []).map((budget: any) => ({
        id: budget._id || budget.id,
        name: budget.name,
        fiscalYear: budget.fiscalYear,
        status: budget.status,
        totalExpense: budget.totalExpense || 0,
        totalActualExpense: budget.totalActualExpense || 0
      }));

      const allCategories = (categoriesData.categories || []).map((category: any) => ({
        id: category._id || category.id,
        name: category.name,
        type: category.type,
        budget: category.budget,
        budgetedAmount: category.budgetedAmount || 0,
        actualAmount: category.actualAmount || 0
      }));

      // Cache the data
      localStorage.setItem('expenditure-form-budgets', JSON.stringify(processedBudgets));
      localStorage.setItem('expenditure-form-budget-categories', JSON.stringify(allCategories));
      localStorage.setItem('expenditure-form-cache-timestamp', Date.now().toString());

      this.budgets = processedBudgets;
      this.budgetCategories = allCategories;
      this.isLoaded = true;

      return { budgets: this.budgets, budgetCategories: this.budgetCategories };
    } catch (error) {
      console.error('Error loading budget data:', error);
      return { budgets: [], budgetCategories: [] };
    } finally {
      this.isLoading = false;
    }
  }
}

export function ProgressiveExpenditureForm({
  expenditure,
  onSubmit,
  onCancel,
  isLoading = false
}: ProgressiveExpenditureFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [budgetDataState, setBudgetDataState] = useState<{
    budgets: any[];
    budgetCategories: any[];
    isLoading: boolean;
    isLoaded: boolean;
  }>({
    budgets: [],
    budgetCategories: [],
    isLoading: false,
    isLoaded: false
  });

  // Initialize form data with instant defaults (no backend dependency)
  const [formData, setFormData] = useState<ProgressiveExpenditureFormData>(() => ({
    date: expenditure?.date ? new Date(expenditure.date) : new Date(),
    category: expenditure?.category || 'office_supplies',
    subcategory: expenditure?.subcategory || '',
    amount: expenditure?.amount?.toString() || '',
    reference: expenditure?.reference || '',
    description: expenditure?.description || '',
    fiscalYear: expenditure?.fiscalYear || '2024-2025',
    status: expenditure?.status || 'draft',
    paymentMethod: expenditure?.paymentMethod || 'bank_transfer',
    vendor: expenditure?.vendor || '',
    department: expenditure?.department || 'general',
    costCenter: expenditure?.costCenter || '',
    budget: expenditure?.budget || '',
    budgetCategory: expenditure?.budgetCategory || '',
    budgetSubcategory: expenditure?.budgetSubcategory || '',
    appliedToBudget: expenditure?.appliedToBudget ?? true,
    notes: expenditure?.notes || '',
  }));

  const [errors, setErrors] = useState<Record<string, string>>({});
  const { toast } = useToast();

  // Progressive data loader instance
  const dataLoader = ProgressiveDataLoader.getInstance();

  // Load budget data when budget dropdown is opened
  const handleBudgetDropdownOpen = useCallback(async () => {
    if (budgetDataState.isLoaded || budgetDataState.isLoading) return;

    setBudgetDataState(prev => ({ ...prev, isLoading: true }));

    try {
      const { budgets, budgetCategories } = await dataLoader.loadBudgetData();
      setBudgetDataState({
        budgets,
        budgetCategories,
        isLoading: false,
        isLoaded: true
      });

      // Set default values if not already set
      if (!formData.budget && budgets.length > 0) {
        const defaultBudgetId = budgets[0].id || budgets[0]._id;
        setFormData(prev => ({ ...prev, budget: defaultBudgetId }));
      }
      if (!formData.budgetCategory && budgetCategories.length > 0) {
        const defaultCategoryId = budgetCategories[0].id || budgetCategories[0]._id;
        setFormData(prev => ({ ...prev, budgetCategory: defaultCategoryId }));
      }
    } catch (error) {
      console.error('Error loading budget data:', error);
      setBudgetDataState(prev => ({ ...prev, isLoading: false }));
    }
  }, [budgetDataState.isLoaded, budgetDataState.isLoading, formData.budget, formData.budgetCategory]);

  const updateField = (field: keyof ProgressiveExpenditureFormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error for this field
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.category.trim()) {
      newErrors.category = 'Category is required';
    }
    if (!formData.amount.trim()) {
      newErrors.amount = 'Amount is required';
    } else if (isNaN(Number(formData.amount)) || Number(formData.amount) <= 0) {
      newErrors.amount = 'Amount must be a positive number';
    }
    if (!formData.reference.trim()) {
      newErrors.reference = 'Reference is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);
    try {
      await onSubmit({
        ...formData,
        amount: Number(formData.amount),
      });
      toast({
        title: expenditure ? 'Expenditure updated' : 'Expenditure created',
        description: expenditure 
          ? 'The expenditure has been updated successfully.' 
          : 'The expenditure has been created successfully.',
      });
    } catch (error) {
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'An error occurred',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Receipt className="h-5 w-5" />
            {expenditure ? 'Edit Expenditure' : 'Record New Expenditure'}
          </CardTitle>
          <Badge variant="default" className="text-xs">
            <Zap className="h-3 w-3 mr-1" />
            Progressive Loading
          </Badge>
        </div>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Instant Loading Section - No Backend Dependencies */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-medium">Basic Information</h3>
              <Badge variant="outline" className="text-xs">
                <CheckCircle className="h-3 w-3 mr-1" />
                Instant Load
              </Badge>
            </div>
            <Separator />

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Date */}
              <div className="space-y-2">
                <Label htmlFor="date">Date *</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className="w-full justify-start text-left font-normal"
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {formData.date ? format(formData.date, 'PPP') : 'Pick a date'}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={formData.date}
                      onSelect={(date) => date && updateField('date', date)}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>

              {/* Category */}
              <div className="space-y-2">
                <Label htmlFor="category">Category *</Label>
                <Select
                  value={formData.category}
                  onValueChange={(value) => updateField('category', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select category" />
                  </SelectTrigger>
                  <SelectContent>
                    {INSTANT_FORM_DATA.expenseCategories.map(category => (
                      <SelectItem key={category.value} value={category.value}>
                        {category.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {errors.category && <p className="text-sm text-destructive">{errors.category}</p>}
              </div>

              {/* Amount */}
              <div className="space-y-2">
                <Label htmlFor="amount">Amount (MWK) *</Label>
                <Input
                  id="amount"
                  type="number"
                  step="0.01"
                  placeholder="Enter amount"
                  value={formData.amount}
                  onChange={(e) => updateField('amount', e.target.value)}
                />
                {errors.amount && <p className="text-sm text-destructive">{errors.amount}</p>}
              </div>

              {/* Reference */}
              <div className="space-y-2">
                <Label htmlFor="reference">Reference *</Label>
                <Input
                  id="reference"
                  placeholder="Enter reference number"
                  value={formData.reference}
                  onChange={(e) => updateField('reference', e.target.value)}
                />
                {errors.reference && <p className="text-sm text-destructive">{errors.reference}</p>}
              </div>

              {/* Fiscal Year */}
              <div className="space-y-2">
                <Label htmlFor="fiscalYear">Fiscal Year</Label>
                <Select
                  value={formData.fiscalYear}
                  onValueChange={(value) => updateField('fiscalYear', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select fiscal year" />
                  </SelectTrigger>
                  <SelectContent>
                    {INSTANT_FORM_DATA.fiscalYears.map(year => (
                      <SelectItem key={year.value} value={year.value}>
                        {year.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Status */}
              <div className="space-y-2">
                <Label htmlFor="status">Status</Label>
                <Select
                  value={formData.status}
                  onValueChange={(value) => updateField('status', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                  <SelectContent>
                    {INSTANT_FORM_DATA.statusOptions.map(status => (
                      <SelectItem key={status.value} value={status.value}>
                        {status.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Payment Method */}
              <div className="space-y-2">
                <Label htmlFor="paymentMethod">Payment Method</Label>
                <Select
                  value={formData.paymentMethod || ''}
                  onValueChange={(value) => updateField('paymentMethod', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select payment method" />
                  </SelectTrigger>
                  <SelectContent>
                    {INSTANT_FORM_DATA.paymentMethods.map(method => (
                      <SelectItem key={method.value} value={method.value}>
                        {method.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Department */}
              <div className="space-y-2">
                <Label htmlFor="department">Department</Label>
                <Select
                  value={formData.department || ''}
                  onValueChange={(value) => updateField('department', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select department" />
                  </SelectTrigger>
                  <SelectContent>
                    {INSTANT_FORM_DATA.departments.map(dept => (
                      <SelectItem key={dept.value} value={dept.value}>
                        {dept.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Vendor */}
              <div className="space-y-2">
                <Label htmlFor="vendor">Vendor/Supplier</Label>
                <Input
                  id="vendor"
                  placeholder="Enter vendor name"
                  value={formData.vendor || ''}
                  onChange={(e) => updateField('vendor', e.target.value)}
                />
              </div>

              {/* Cost Center */}
              <div className="space-y-2">
                <Label htmlFor="costCenter">Cost Center</Label>
                <Input
                  id="costCenter"
                  placeholder="Enter cost center"
                  value={formData.costCenter || ''}
                  onChange={(e) => updateField('costCenter', e.target.value)}
                />
              </div>
            </div>
          </div>

          {/* Progressive Loading Section - Backend Dependencies */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-medium">Budget Integration (Optional)</h3>
              <Badge variant="outline" className="text-xs">
                {budgetDataState.isLoaded ? (
                  <>
                    <CheckCircle className="h-3 w-3 mr-1" />
                    Loaded
                  </>
                ) : (
                  <>
                    <Loader2 className="h-3 w-3 mr-1 animate-spin" />
                    On-Demand
                  </>
                )}
              </Badge>
            </div>
            <Separator />

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Budget Selection */}
              <div className="space-y-2">
                <Label htmlFor="budget">Budget</Label>
                {budgetDataState.isLoading ? (
                  <Skeleton className="h-10 w-full" />
                ) : (
                  <Select
                    value={formData.budget}
                    onValueChange={(value) => updateField('budget', value)}
                    onOpenChange={(open) => {
                      if (open) handleBudgetDropdownOpen();
                    }}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select budget (optional)" />
                    </SelectTrigger>
                    <SelectContent>
                      {budgetDataState.budgets.map(budget => (
                        <SelectItem key={budget.id || budget._id} value={budget.id || budget._id}>
                          {budget.name} ({budget.fiscalYear})
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                )}
              </div>

              {/* Budget Category Selection */}
              <div className="space-y-2">
                <Label htmlFor="budgetCategory">Budget Category</Label>
                {budgetDataState.isLoading ? (
                  <Skeleton className="h-10 w-full" />
                ) : (
                  <Select
                    value={formData.budgetCategory}
                    onValueChange={(value) => updateField('budgetCategory', value)}
                    disabled={!budgetDataState.isLoaded}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select category (optional)" />
                    </SelectTrigger>
                    <SelectContent>
                      {budgetDataState.budgetCategories.map(category => (
                        <SelectItem key={category.id || category._id} value={category.id || category._id}>
                          <div className="flex items-center justify-between w-full">
                            <span>{category.name}</span>
                            <span className="text-xs text-muted-foreground ml-2">
                              ({category.type || 'expense'})
                            </span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                )}
              </div>
            </div>
          </div>

          {/* Description */}
          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              placeholder="Enter description (optional)"
              value={formData.description || ''}
              onChange={(e) => updateField('description', e.target.value)}
            />
          </div>

          {/* Notes */}
          <div className="space-y-2">
            <Label htmlFor="notes">Notes</Label>
            <Textarea
              id="notes"
              placeholder="Enter additional notes (optional)"
              value={formData.notes || ''}
              onChange={(e) => updateField('notes', e.target.value)}
            />
          </div>
        </form>
      </CardContent>
      <CardFooter className="flex justify-end space-x-2">
        <Button
          type="button"
          variant="outline"
          onClick={onCancel}
          disabled={isSubmitting}
        >
          Cancel
        </Button>
        <Button
          type="submit"
          onClick={handleSubmit}
          disabled={isSubmitting || isLoading}
        >
          {isSubmitting ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              {expenditure ? 'Updating...' : 'Creating...'}
            </>
          ) : (
            expenditure ? 'Update Expenditure' : 'Create Expenditure'
          )}
        </Button>
      </CardFooter>
    </Card>
  );
}
