// app/api/accounting/expense/template/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import * as XLSX from 'xlsx';

export const runtime = 'nodejs';

/**
 * GET /api/accounting/expense/template
 * Download Excel template for bulk expenditure import
 */
export async function GET(req: NextRequest): Promise<Response> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Create template data
    const templateData = [
      // Header row
      [
        'Date',
        'Category',
        'Subcategory',
        'Amount',
        'Reference',
        'Description',
        'Fiscal Year',
        'Status',
        'Payment Method',
        'Vendor',
        'Department',
        'Cost Center',
        'Budget',
        'Budget Category',
        'Budget Subcategory',
        'Applied to Budget',
        'Notes'
      ],
      // Example row 1
      [
        '2024-12-01',
        'office_supplies',
        'Stationery',
        1500.00,
        'EXP-2024-001',
        'Office stationery purchase',
        '2024-2025',
        'draft',
        'bank_transfer',
        'ABC Supplies Ltd',
        'administration',
        'ADMIN-001',
        '',
        '',
        '',
        'TRUE',
        'Monthly office supplies'
      ],
      // Example row 2
      [
        '2024-12-02',
        'travel_transport',
        'Local Travel',
        2500.00,
        'EXP-2024-002',
        'Staff travel expenses',
        '2024-2025',
        'pending_approval',
        'cash',
        'Transport Services',
        'operations',
        'OPS-001',
        '',
        '',
        '',
        'TRUE',
        'Field visit transportation'
      ],
      // Example row 3
      [
        '2024-12-03',
        'utilities',
        'Electricity',
        15000.00,
        'EXP-2024-003',
        'Monthly electricity bill',
        '2024-2025',
        'approved',
        'bank_transfer',
        'ESCOM',
        'general',
        'GEN-001',
        '',
        '',
        '',
        'TRUE',
        'December electricity payment'
      ]
    ];

    // Create workbook and worksheet
    const workbook = XLSX.utils.book_new();
    const worksheet = XLSX.utils.aoa_to_sheet(templateData);

    // Set column widths
    const columnWidths = [
      { wch: 12 }, // Date
      { wch: 18 }, // Category
      { wch: 15 }, // Subcategory
      { wch: 12 }, // Amount
      { wch: 15 }, // Reference
      { wch: 25 }, // Description
      { wch: 12 }, // Fiscal Year
      { wch: 18 }, // Status
      { wch: 15 }, // Payment Method
      { wch: 20 }, // Vendor
      { wch: 15 }, // Department
      { wch: 12 }, // Cost Center
      { wch: 15 }, // Budget
      { wch: 18 }, // Budget Category
      { wch: 20 }, // Budget Subcategory
      { wch: 18 }, // Applied to Budget
      { wch: 25 }  // Notes
    ];
    worksheet['!cols'] = columnWidths;

    // Add the worksheet to workbook
    XLSX.utils.book_append_sheet(workbook, worksheet, 'Expenditure Template');

    // Create instructions worksheet
    const instructionsData = [
      ['EXPENDITURE IMPORT TEMPLATE INSTRUCTIONS'],
      [''],
      ['REQUIRED FIELDS:'],
      ['Date', 'Format: YYYY-MM-DD (e.g., 2024-12-01)'],
      ['Category', 'Valid values: office_supplies, travel_transport, utilities, professional_services, equipment, maintenance, training, communications, insurance, personnel, administrative, other'],
      ['Amount', 'Numeric value (e.g., 1500.00)'],
      ['Reference', 'Unique reference number (e.g., EXP-2024-001)'],
      ['Fiscal Year', 'Format: YYYY-YYYY (e.g., 2024-2025)'],
      [''],
      ['OPTIONAL FIELDS:'],
      ['Subcategory', 'Additional category specification'],
      ['Description', 'Detailed description of the expenditure'],
      ['Status', 'Valid values: draft, pending_approval, approved, rejected, paid, cancelled (defaults to draft)'],
      ['Payment Method', 'Valid values: bank_transfer, check, cash, credit_card, mobile_money'],
      ['Vendor', 'Supplier or vendor name'],
      ['Department', 'Valid values: administration, finance, hr, it, operations, general'],
      ['Cost Center', 'Cost center code'],
      ['Budget', 'Budget ID (leave empty for auto-assignment)'],
      ['Budget Category', 'Budget category ID (leave empty for auto-assignment)'],
      ['Budget Subcategory', 'Budget subcategory ID (leave empty for auto-assignment)'],
      ['Applied to Budget', 'TRUE/FALSE (defaults to TRUE)'],
      ['Notes', 'Additional notes or comments'],
      [''],
      ['IMPORTANT NOTES:'],
      ['1. Do not modify the header row'],
      ['2. Date must be in YYYY-MM-DD format'],
      ['3. Amount must be numeric (no currency symbols)'],
      ['4. Reference numbers should be unique'],
      ['5. Category values are case-sensitive'],
      ['6. Applied to Budget: use TRUE/FALSE, 1/0, or YES/NO'],
      ['7. Empty rows will be skipped'],
      ['8. Maximum file size: 10MB'],
      ['9. Supported formats: .xlsx, .xls, .csv'],
      [''],
      ['CATEGORY OPTIONS:'],
      ['office_supplies - Office supplies and stationery'],
      ['travel_transport - Travel and transportation expenses'],
      ['utilities - Utility bills (electricity, water, internet)'],
      ['professional_services - Professional and consulting services'],
      ['equipment - Equipment purchases and rentals'],
      ['maintenance - Maintenance and repairs'],
      ['training - Training and development'],
      ['communications - Communication expenses'],
      ['insurance - Insurance premiums'],
      ['personnel - Personnel-related expenses'],
      ['administrative - Administrative expenses'],
      ['other - Other miscellaneous expenses'],
      [''],
      ['STATUS OPTIONS:'],
      ['draft - Initial state, can be edited'],
      ['pending_approval - Submitted for approval'],
      ['approved - Approved for payment'],
      ['rejected - Rejected, needs revision'],
      ['paid - Payment processed'],
      ['cancelled - Cancelled expenditure']
    ];

    const instructionsWorksheet = XLSX.utils.aoa_to_sheet(instructionsData);
    instructionsWorksheet['!cols'] = [{ wch: 25 }, { wch: 60 }];
    XLSX.utils.book_append_sheet(workbook, instructionsWorksheet, 'Instructions');

    // Generate Excel file buffer
    const excelBuffer = XLSX.write(workbook, { 
      type: 'buffer', 
      bookType: 'xlsx',
      compression: true 
    });

    // Create response with proper headers
    const response = new NextResponse(excelBuffer);
    response.headers.set('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    response.headers.set('Content-Disposition', 'attachment; filename="expenditure_import_template.xlsx"');
    response.headers.set('Cache-Control', 'no-cache, no-store, must-revalidate');
    response.headers.set('Pragma', 'no-cache');
    response.headers.set('Expires', '0');

    return response;

  } catch (error: unknown) {
    console.error('Error generating expenditure template:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal Server Error' },
      { status: 500 }
    );
  }
}
