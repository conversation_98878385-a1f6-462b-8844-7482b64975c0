// types/expenditure.ts
export interface Budget {
  id: string;
  _id?: string;
  name: string;
  fiscalYear: string;
  status: 'active' | 'inactive' | 'draft' | 'approved';
  totalExpense?: number;
  totalActualExpense?: number;
  totalIncome?: number;
  totalActualIncome?: number;
  categories?: BudgetCategory[];
  createdAt?: Date | string;
  updatedAt?: Date | string;
}

export interface BudgetCategory {
  id: string;
  _id?: string;
  name: string;
  description?: string;
  type: 'income' | 'expense' | 'expenditure' | 'revenue';
  budgetId?: string;
  budget?: string;
  budgetName?: string;
  budgetedAmount: number;
  actualAmount: number;
  variance?: number;
  utilizationPercentage?: number;
  isActive?: boolean;
  createdAt?: Date | string;
  updatedAt?: Date | string;
}

export interface ExpenditureFormData {
  date: Date;
  description: string;
  subcategory?: string;
  amount: string;
  reference: string;
  fiscalYear: string;
  status: 'draft' | 'pending_approval' | 'approved' | 'paid' | 'rejected';
  paymentMethod?: 'cash' | 'bank_transfer' | 'cheque' | 'mobile_money' | 'other';
  vendor?: string;
  department?: string;
  costCenter?: string;
  budget: string;
  budgetCategory: string;
  budgetSubcategory?: string;
  appliedToBudget?: boolean;
  notes?: string;
}

export interface BudgetDataState {
  budgets: Budget[];
  budgetCategories: BudgetCategory[];
  isLoading: boolean;
  isLoaded: boolean;
}

export interface FormOptions {
  budgets: Budget[];
  budgetCategories: BudgetCategory[];
  bankAccounts: BankAccount[];
  paymentMethods: PaymentMethod[];
}

export interface BankAccount {
  id: string;
  _id?: string;
  name: string;
  accountNumber: string;
  bankName: string;
  isActive: boolean;
}

export interface PaymentMethod {
  id: string;
  _id?: string;
  name: string;
  code: string;
  isActive: boolean;
}

export interface ExpenditureStats {
  totalExpenditure: number;
  actualExpenditure: number;
  budgetedExpenditure: number;
  plannedBudgetExpenditure: number;
  expenditurePercentage: number;
  yearOverYearChange: number;
  budgetVariance: number;
  budgetVariancePercentage: number;
  actualBudgetVariance: number;
  actualBudgetVariancePercentage: number;
  trendDirection: 'up' | 'down' | 'stable';
  expenditureByCategory: ExpenditureCategoryStats[];
  expenditureByStatus: ExpenditureStatusStats[];
  monthlyExpenditure: MonthlyExpenditureData[];
  totalRecords: number;
  fiscalYear: string;
  budgetId?: string;
  generatedAt: string;
}

export interface ExpenditureCategoryStats {
  category: string;
  amount: number;
  count: number;
  budgeted: number;
  actual: number;
}

export interface ExpenditureStatusStats {
  status: string;
  amount: number;
  count: number;
}

export interface MonthlyExpenditureData {
  month: string;
  amount: number;
  budgeted: number;
  actual: number;
  variance: number;
  utilizationPercentage: number;
  count: number;
}

export interface FiscalYear {
  value: string;
  label: string;
  startDate: Date;
  endDate: Date;
  isCurrent: boolean;
}

export interface ExpenditureFormErrors {
  description?: string;
  amount?: string;
  reference?: string;
  budget?: string;
  budgetCategory?: string;
  [key: string]: string | undefined;
}

export interface ExpenditureApiResponse {
  categories: BudgetCategory[];
  total: number;
  budgetId?: string;
  type: string;
  fiscalYear?: string;
}

export interface MonthlyExpenditureResponse {
  monthlyExpenditure: MonthlyExpenditureData[];
  summary: {
    totalAmount: number;
    totalActual: number;
    totalBudgeted: number;
    averageMonthly: number;
    averageActual: number;
    totalVariance: number;
    overallUtilization: number;
    monthsWithData: number;
    totalTransactions: number;
  };
  fiscalYear: string;
  budgetId?: string;
  generatedAt: string;
}
