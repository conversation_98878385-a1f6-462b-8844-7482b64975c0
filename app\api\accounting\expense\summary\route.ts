// app/api/accounting/expense/summary/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { connectToDatabase } from '@/lib/database';
import { logger, LogCategory } from '@/lib/backend/utils/logger';
import Expense from '@/models/accounting/Expense';
import BudgetExpenditure from '@/models/accounting/BudgetExpenditure';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';

export const runtime = 'nodejs';

/**
 * GET /api/accounting/expense/summary
 * Get expenditure summary statistics
 */
export async function GET(req: NextRequest): Promise<Response> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT,
      UserRole.FINANCE_DIRECTOR,
      UserRole.HR_MANAGER
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Parse query parameters
    const { searchParams } = new URL(req.url);
    const fiscalYear = searchParams.get('fiscalYear') || '2025-2026';
    const budgetId = searchParams.get('budgetId');
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');

    logger.info('Fetching expenditure summary', LogCategory.ACCOUNTING, {
      userId: user.id,
      fiscalYear,
      budgetId,
      startDate,
      endDate
    });

    // Build query for expenses
    const expenseQuery: any = {
      fiscalYear
    };

    if (budgetId) {
      expenseQuery.budget = budgetId;
    }

    if (startDate || endDate) {
      expenseQuery.date = {};
      if (startDate) expenseQuery.date.$gte = new Date(startDate);
      if (endDate) expenseQuery.date.$lte = new Date(endDate);
    }

    // Fetch expense data
    const expenses = await Expense.find(expenseQuery).lean();

    // Calculate total expenditure
    const totalExpenditure = expenses.reduce((sum, expense) => sum + (expense.amount || 0), 0);

    // Calculate actual expenditure (only paid expenses)
    const actualExpenditure = expenses
      .filter(expense => expense.status === 'paid')
      .reduce((sum, expense) => sum + (expense.amount || 0), 0);

    // Calculate budgeted expenditure from BudgetExpenditure
    const budgetExpenditureQuery: any = {};
    if (budgetId) {
      budgetExpenditureQuery.budget = budgetId;
    }
    if (fiscalYear) {
      budgetExpenditureQuery.fiscalYear = fiscalYear;
    }

    const budgetExpenditures = await BudgetExpenditure.find(budgetExpenditureQuery).lean();
    const budgetedExpenditure = budgetExpenditures.reduce((sum, item) => sum + (item.budgetedAmount || 0), 0);

    // Calculate expenditure by category
    const expenditureByCategory = expenses.reduce((acc, expense) => {
      const category = expense.category || 'uncategorized';
      if (!acc[category]) {
        acc[category] = {
          category,
          amount: 0,
          count: 0,
          budgeted: 0,
          actual: 0
        };
      }
      acc[category].amount += expense.amount || 0;
      acc[category].count += 1;
      
      if (expense.status === 'paid') {
        acc[category].actual += expense.amount || 0;
      }
      
      return acc;
    }, {} as Record<string, any>);

    // Add budgeted amounts by category from BudgetExpenditure
    budgetExpenditures.forEach(budgetExp => {
      const category = budgetExp.category || 'uncategorized';
      if (expenditureByCategory[category]) {
        expenditureByCategory[category].budgeted += budgetExp.budgetedAmount || 0;
      }
    });

    // Calculate expenditure by status
    const expenditureByStatus = expenses.reduce((acc, expense) => {
      const status = expense.status || 'unknown';
      if (!acc[status]) {
        acc[status] = {
          status,
          amount: 0,
          count: 0
        };
      }
      acc[status].amount += expense.amount || 0;
      acc[status].count += 1;
      return acc;
    }, {} as Record<string, any>);

    // Calculate year-over-year change
    const previousFiscalYear = getPreviousFiscalYear(fiscalYear);
    const previousYearExpenses = await Expense.find({
      fiscalYear: previousFiscalYear,
      ...(budgetId && { budget: budgetId })
    }).lean();
    
    const previousYearTotal = previousYearExpenses.reduce((sum, expense) => sum + (expense.amount || 0), 0);
    const yearOverYearChange = previousYearTotal > 0 ? ((totalExpenditure - previousYearTotal) / previousYearTotal) * 100 : 0;

    // Calculate budget variance
    const budgetVariance = actualExpenditure - budgetedExpenditure;
    const budgetVariancePercentage = budgetedExpenditure > 0 ? (budgetVariance / budgetedExpenditure) * 100 : 0;

    // Determine trend direction
    let trendDirection: 'up' | 'down' | 'stable' = 'stable';
    if (yearOverYearChange > 5) trendDirection = 'up';
    else if (yearOverYearChange < -5) trendDirection = 'down';

    const summary = {
      totalExpenditure,
      actualExpenditure,
      budgetedExpenditure,
      plannedBudgetExpenditure: budgetedExpenditure,
      expenditurePercentage: budgetedExpenditure > 0 ? (actualExpenditure / budgetedExpenditure) * 100 : 0,
      yearOverYearChange,
      budgetVariance,
      budgetVariancePercentage,
      actualBudgetVariance: budgetVariance,
      actualBudgetVariancePercentage: budgetVariancePercentage,
      trendDirection,
      expenditureByCategory: Object.values(expenditureByCategory),
      expenditureByStatus: Object.values(expenditureByStatus),
      totalRecords: expenses.length,
      fiscalYear,
      budgetId,
      generatedAt: new Date().toISOString()
    };

    logger.info('Expenditure summary generated successfully', LogCategory.ACCOUNTING, {
      userId: user.id,
      fiscalYear,
      totalExpenditure,
      totalRecords: expenses.length
    });

    return NextResponse.json(summary);

  } catch (error: unknown) {
    logger.error('Error fetching expenditure summary', LogCategory.ACCOUNTING, error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal Server Error' },
      { status: 500 }
    );
  }
}

/**
 * Helper function to get previous fiscal year
 */
function getPreviousFiscalYear(currentFiscalYear: string): string {
  const [startYear] = currentFiscalYear.split('-');
  const prevStartYear = parseInt(startYear) - 1;
  const prevEndYear = prevStartYear + 1;
  return `${prevStartYear}-${prevEndYear}`;
}
