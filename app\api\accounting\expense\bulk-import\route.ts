// app/api/accounting/expense/bulk-import/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { connectToDatabase } from '@/lib/database';
import { logger } from '@/lib/utils/logger';
import { LogCategory } from '@/types/logging';
import { z } from 'zod';
import * as XLSX from 'xlsx';
import Expense from '@/models/accounting/Expense';
import { budgetExpenditureIntegrationService } from '@/lib/services/accounting/budget-expenditure-integration';
import { budgetTransactionService } from '@/lib/services/accounting/budget-transaction-service';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';

export const runtime = 'nodejs';

// Validation schema for expenditure data
const expenditureSchema = z.object({
  date: z.string().refine((val) => !isNaN(Date.parse(val)), {
    message: "Invalid date format. Use YYYY-MM-DD",
  }),
  category: z.string().min(1, "Category is required"),
  subcategory: z.string().optional(),
  amount: z.number().positive("Amount must be positive"),
  reference: z.string().min(2, "Reference must be at least 2 characters"),
  description: z.string().optional(),
  fiscalYear: z.string().min(4, "Fiscal year is required"),
  status: z.enum(['draft', 'pending_approval', 'approved', 'rejected', 'paid', 'cancelled']).default('draft'),
  paymentMethod: z.string().optional(),
  vendor: z.string().optional(),
  department: z.string().optional(),
  costCenter: z.string().optional(),
  budget: z.string().optional(),
  budgetCategory: z.string().optional(),
  budgetSubcategory: z.string().optional(),
  appliedToBudget: z.boolean().optional().default(true),
  notes: z.string().optional(),
});

interface ProcessedRow {
  row: number;
  data: any;
  expenditure?: any;
  errors: string[];
  warnings: string[];
}

/**
 * POST /api/accounting/expense/bulk-import
 * Import multiple expenditures from Excel/CSV file
 */
export async function POST(req: NextRequest): Promise<Response> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions for bulk import' },
        { status: 403 }
      );
    }

    // Parse form data
    const formData = await req.formData();
    const file = formData.get('file') as File;

    if (!file) {
      return NextResponse.json(
        { error: 'No file provided' },
        { status: 400 }
      );
    }

    // Validate file type
    const validTypes = [
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'application/vnd.ms-excel',
      'text/csv'
    ];

    if (!validTypes.includes(file.type)) {
      return NextResponse.json(
        { error: 'Invalid file type. Please upload Excel (.xlsx, .xls) or CSV file' },
        { status: 400 }
      );
    }

    // Validate file size (max 10MB)
    if (file.size > 10 * 1024 * 1024) {
      return NextResponse.json(
        { error: 'File size too large. Maximum size is 10MB' },
        { status: 400 }
      );
    }

    logger.info('Starting bulk expenditure import', LogCategory.ACCOUNTING, {
      userId: user.id,
      fileName: file.name,
      fileSize: file.size,
      fileType: file.type
    });

    // Read file content
    const buffer = await file.arrayBuffer();
    const workbook = XLSX.read(buffer, { type: 'buffer' });
    const sheetName = workbook.SheetNames[0];
    const worksheet = workbook.Sheets[sheetName];
    const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });

    if (jsonData.length < 2) {
      return NextResponse.json(
        { error: 'File must contain at least a header row and one data row' },
        { status: 400 }
      );
    }

    // Extract headers and data
    const headers = jsonData[0] as string[];
    const dataRows = jsonData.slice(1);

    // Map headers to expected field names
    const headerMap: Record<string, string> = {
      'date': 'date',
      'category': 'category',
      'subcategory': 'subcategory',
      'amount': 'amount',
      'reference': 'reference',
      'description': 'description',
      'fiscal year': 'fiscalYear',
      'fiscalyear': 'fiscalYear',
      'status': 'status',
      'payment method': 'paymentMethod',
      'paymentmethod': 'paymentMethod',
      'vendor': 'vendor',
      'supplier': 'vendor',
      'department': 'department',
      'cost center': 'costCenter',
      'costcenter': 'costCenter',
      'budget': 'budget',
      'budget category': 'budgetCategory',
      'budgetcategory': 'budgetCategory',
      'budget subcategory': 'budgetSubcategory',
      'budgetsubcategory': 'budgetSubcategory',
      'applied to budget': 'appliedToBudget',
      'appliedtobudget': 'appliedToBudget',
      'notes': 'notes'
    };

    // Normalize headers
    const normalizedHeaders = headers.map(header => 
      headerMap[header.toLowerCase().trim()] || header.toLowerCase().replace(/\s+/g, '')
    );

    // Process each row
    const results: ProcessedRow[] = [];

    for (let i = 0; i < dataRows.length; i++) {
      const row = dataRows[i] as any[];
      const rowNumber = i + 2; // +2 because we start from row 1 and skip header
      const rowData: any = {};
      const errors: string[] = [];
      const warnings: string[] = [];

      // Map row data to object
      normalizedHeaders.forEach((header, index) => {
        if (header && row[index] !== undefined && row[index] !== null && row[index] !== '') {
          rowData[header] = row[index];
        }
      });

      // Skip empty rows
      if (Object.keys(rowData).length === 0) {
        continue;
      }

      // Convert and validate data types
      try {
        // Convert amount to number
        if (rowData.amount) {
          const amount = parseFloat(rowData.amount.toString().replace(/[^\d.-]/g, ''));
          if (isNaN(amount)) {
            errors.push('Invalid amount format');
          } else {
            rowData.amount = amount;
          }
        }

        // Convert appliedToBudget to boolean
        if (rowData.appliedToBudget !== undefined) {
          const value = rowData.appliedToBudget.toString().toLowerCase();
          rowData.appliedToBudget = ['true', '1', 'yes', 'y'].includes(value);
        }

        // Validate date format
        if (rowData.date) {
          const dateStr = rowData.date.toString();
          if (isNaN(Date.parse(dateStr))) {
            errors.push('Invalid date format. Use YYYY-MM-DD');
          }
        }

        // Set defaults
        if (!rowData.fiscalYear) {
          rowData.fiscalYear = '2024-2025';
          warnings.push('Fiscal year not specified, defaulting to 2024-2025');
        }

        if (!rowData.status) {
          rowData.status = 'draft';
          warnings.push('Status not specified, defaulting to draft');
        }

        if (rowData.appliedToBudget === undefined) {
          rowData.appliedToBudget = true;
          warnings.push('Applied to budget not specified, defaulting to true');
        }

        // Validate required fields
        const requiredFields = ['date', 'category', 'amount', 'reference'];
        for (const field of requiredFields) {
          if (!rowData[field]) {
            errors.push(`Missing required field: ${field}`);
          }
        }

        // Validate with schema if no errors
        let expenditure = null;
        if (errors.length === 0) {
          try {
            const validatedData = expenditureSchema.parse(rowData);
            expenditure = {
              ...validatedData,
              date: new Date(validatedData.date),
              createdBy: user.id
            };
          } catch (validationError) {
            if (validationError instanceof z.ZodError) {
              errors.push(...validationError.errors.map(err => `${err.path.join('.')}: ${err.message}`));
            } else {
              errors.push('Validation failed');
            }
          }
        }

        results.push({
          row: rowNumber,
          data: rowData,
          expenditure,
          errors,
          warnings
        });

      } catch (error) {
        errors.push(`Row processing error: ${error instanceof Error ? error.message : 'Unknown error'}`);
        results.push({
          row: rowNumber,
          data: rowData,
          errors,
          warnings
        });
      }
    }

    // Import valid records
    const importedExpenditures = [];
    const importErrors = [];

    for (const result of results) {
      if (result.expenditure) {
        try {
          const expenditure = await Expense.create(result.expenditure);
          importedExpenditures.push(expenditure);

          // Run budget integration in background
          if (expenditure.appliedToBudget) {
            Promise.allSettled([
              budgetExpenditureIntegrationService.createExpenditureAsBudgetItem(expenditure),
              budgetTransactionService.linkExpenseToBudget(
                expenditure._id.toString(),
                expenditure.budget?.toString(),
                expenditure.budgetCategory?.toString(),
                expenditure.budgetSubcategory?.toString()
              )
            ]).catch(error => {
              logger.error('Background budget integration failed during import', LogCategory.ACCOUNTING, {
                expenditureId: expenditure._id,
                error: error.message
              });
            });
          }

        } catch (error) {
          importErrors.push({
            row: result.row,
            error: error instanceof Error ? error.message : 'Failed to create expenditure',
            data: result.data
          });
        }
      }
    }

    // Compile all errors and warnings
    const allErrors = [
      ...results.filter(r => r.errors.length > 0).map(r => ({
        row: r.row,
        error: r.errors.join(', '),
        data: r.data
      })),
      ...importErrors
    ];

    const allWarnings = results.filter(r => r.warnings.length > 0).map(r => ({
      row: r.row,
      warning: r.warnings.join(', '),
      data: r.data
    }));

    const successCount = importedExpenditures.length;
    const errorCount = allErrors.length;
    const totalProcessed = results.length;

    logger.info('Bulk expenditure import completed', LogCategory.ACCOUNTING, {
      userId: user.id,
      fileName: file.name,
      totalProcessed,
      successCount,
      errorCount,
      warningCount: allWarnings.length
    });

    return NextResponse.json({
      success: errorCount === 0,
      message: errorCount === 0 
        ? `Successfully imported ${successCount} expenditures`
        : `Import completed with ${errorCount} errors and ${successCount} successful imports`,
      successCount,
      errorCount,
      warningCount: allWarnings.length,
      totalProcessed,
      errors: allErrors,
      warnings: allWarnings,
      importedExpenditures: importedExpenditures.map(exp => ({
        id: exp._id,
        reference: exp.reference,
        amount: exp.amount,
        category: exp.category
      }))
    });

  } catch (error: unknown) {
    logger.error('Error in bulk expenditure import', LogCategory.ACCOUNTING, error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal Server Error' },
      { status: 500 }
    );
  }
}
