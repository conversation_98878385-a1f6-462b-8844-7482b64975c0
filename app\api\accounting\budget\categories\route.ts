// app/api/accounting/budget/categories/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { connectToDatabase } from '@/lib/database';
import { logger, LogCategory } from '@/lib/backend/utils/logger';
import Budget from '@/models/accounting/Budget';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';

export const runtime = 'nodejs';

/**
 * GET /api/accounting/budget/categories
 * Get budget categories for a specific budget and type
 */
export async function GET(req: NextRequest): Promise<Response> {
  try {
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT,
      UserRole.FINANCE_DIRECTOR,
      UserRole.HR_MANAGER
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Parse query parameters
    const { searchParams } = new URL(req.url);
    const budgetId = searchParams.get('budgetId');
    const type = searchParams.get('type') || 'expense'; // 'income' or 'expense'
    const fiscalYear = searchParams.get('fiscalYear');

    logger.info('Fetching budget categories', LogCategory.ACCOUNTING, {
      userId: user.id,
      budgetId,
      type,
      fiscalYear
    });

    let categories: any[] = [];

    if (budgetId) {
      // Get categories from specific budget
      const budget = await Budget.findById(budgetId).lean();
      
      if (!budget) {
        return NextResponse.json({ error: 'Budget not found' }, { status: 404 });
      }

      // Filter categories by type
      categories = budget.categories?.filter((category: any) => {
        if (type === 'expense') {
          return category.type === 'expenditure' || category.type === 'expense';
        } else if (type === 'income') {
          return category.type === 'income' || category.type === 'revenue';
        }
        return true;
      }) || [];

    } else {
      // Get categories from all active budgets
      const query: any = { status: 'active' };
      if (fiscalYear) {
        query.fiscalYear = fiscalYear;
      }

      const budgets = await Budget.find(query).lean();
      
      // Collect all categories from all budgets
      const allCategories: any[] = [];
      budgets.forEach(budget => {
        if (budget.categories) {
          budget.categories.forEach((category: any) => {
            if (type === 'expense') {
              if (category.type === 'expenditure' || category.type === 'expense') {
                allCategories.push({
                  ...category,
                  budgetId: budget._id,
                  budgetName: budget.name
                });
              }
            } else if (type === 'income') {
              if (category.type === 'income' || category.type === 'revenue') {
                allCategories.push({
                  ...category,
                  budgetId: budget._id,
                  budgetName: budget.name
                });
              }
            }
          });
        }
      });

      // Remove duplicates by name and sort
      const uniqueCategories = allCategories.reduce((acc, category) => {
        const existing = acc.find((c: any) => c.name === category.name);
        if (!existing) {
          acc.push(category);
        }
        return acc;
      }, []);

      categories = uniqueCategories.sort((a, b) => a.name.localeCompare(b.name));
    }

    // Format categories for frontend consumption
    const formattedCategories = categories.map(category => ({
      id: category.id || category._id,
      name: category.name,
      description: category.description,
      type: category.type,
      budgetedAmount: category.budgetedAmount || 0,
      actualAmount: category.actualAmount || 0,
      variance: (category.actualAmount || 0) - (category.budgetedAmount || 0),
      utilizationPercentage: category.budgetedAmount > 0 
        ? ((category.actualAmount || 0) / category.budgetedAmount) * 100 
        : 0,
      budgetId: category.budgetId || budgetId,
      budgetName: category.budgetName,
      isActive: category.isActive !== false,
      createdAt: category.createdAt,
      updatedAt: category.updatedAt
    }));

    logger.info('Budget categories retrieved successfully', LogCategory.ACCOUNTING, {
      userId: user.id,
      budgetId,
      type,
      categoriesCount: formattedCategories.length
    });

    return NextResponse.json({
      categories: formattedCategories,
      total: formattedCategories.length,
      budgetId,
      type,
      fiscalYear
    });

  } catch (error: unknown) {
    logger.error('Error fetching budget categories', LogCategory.ACCOUNTING, error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal Server Error' },
      { status: 500 }
    );
  }
}
