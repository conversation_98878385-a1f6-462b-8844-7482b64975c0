// components\accounting\expenditure\expense-table.tsx
"use client";

import React, { useState } from 'react';
import {
  ColumnDef,
  ColumnFiltersState,
  SortingState,
  VisibilityState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table";
import { format } from "date-fns";
import {
  ArrowUpDown,
  ChevronDown,
  Download,
  Filter,
  MoreHorizontal,
  Search,
  FileText,
} from "lucide-react";

import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";

import { Expense } from "@/types/accounting";
import { useExpense } from "@/lib/hooks/accounting/use-expense";
import { useBudget } from "@/lib/hooks/accounting/use-budget";
import { Skeleton } from "@/components/ui/skeleton";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { BudgetBadge } from "@/components/accounting/shared/budget-badge";
import { useIncomeStore } from '@/lib/stores/enhanced-income-store';

// Format currency
const formatCurrency = (value: number) => {
  return new Intl.NumberFormat('en-MW', {
    style: 'currency',
    currency: 'MWK',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(value);
};

// Define the columns for the expense table
export const columns: ColumnDef<Expense>[] = [
  {
    accessorKey: "date",
    header: ({ column }) => {
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Date
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      const date = row.getValue("date") as Date;
      return <div>{format(date, "PPP")}</div>;
    },
  },
  {
    accessorKey: "reference",
    header: "Reference",
    cell: ({ row }) => <div>{row.getValue("reference")}</div>,
  },
  {
    accessorKey: "category",
    header: "Category",
    cell: ({ row }) => {
      const category = row.getValue("category") as string;
      const subcategory = row.original.subcategory;
      return (
        <div>
          <div className="font-medium">{category}</div>
          {subcategory && (
            <div className="text-xs text-muted-foreground">{subcategory}</div>
          )}
        </div>
      );
    },
  },
  {
    accessorKey: "description",
    header: "Description",
    cell: ({ row }) => {
      const description = row.getValue("description") as string | undefined;
      return (
        <div className="max-w-[300px] truncate" title={description}>
          {description || "—"}
        </div>
      );
    },
  },
  {
    accessorKey: "budget",
    header: "Budget",
    cell: ({ row }) => {
      const budgetId = row.original.budget;
      const budgetCategory = row.original.budgetCategory;

      if (!budgetId || !budgetCategory) {
        return <div className="text-muted-foreground text-xs">Not budgeted</div>;
      }

      // Calculate utilization percentage (mock data for now)
      // In a real implementation, this would come from the API
      const expense = row.original as any;
      const utilizationPercentage = expense.budgetUtilization || 75; // Default to 75%

      // Determine trend (mock data for now)
      const trends: ('up' | 'down' | 'neutral')[] = ['up', 'down', 'neutral'];
      const trend = expense.budgetTrend || trends[0]; // Default to 'up'

      return (
        <BudgetBadge
          type="expense"
          budgetName={expense.budgetName || "Budget"}
          categoryName={expense.budgetCategoryName || "Budget Category"}
          utilizationPercentage={utilizationPercentage}
          trend={trend}
          amount={parseFloat(row.getValue("amount"))}
          compact={true}
        />
      );
    },
  },
  {
    accessorKey: "amount",
    header: ({ column }) => {
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          className="justify-end"
        >
          Amount
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      const amount = parseFloat(row.getValue("amount"));
      return (
        <div className="text-right font-medium">{formatCurrency(amount)}</div>
      );
    },
  },
  {
    accessorKey: "status",
    header: "Status",
    cell: ({ row }) => {
      const status = row.getValue("status") as string;

      const statusMap: Record<string, { label: string; variant: "default" | "outline" | "secondary" | "destructive" }> = {
        draft: { label: "Draft", variant: "outline" },
        pending_approval: { label: "Pending", variant: "default" },
        approved: { label: "Approved", variant: "default" },
        rejected: { label: "Rejected", variant: "destructive" },
        paid: { label: "Paid", variant: "secondary" },
        cancelled: { label: "Cancelled", variant: "destructive" },
      };

      const { label, variant } = statusMap[status] || { label: status, variant: "default" };

      return (
        <Badge variant={variant}>
          {label}
        </Badge>
      );
    },
  },
  {
    accessorKey: "paymentMethod",
    header: "Payment Method",
    cell: ({ row }) => {
      const paymentMethod = row.getValue("paymentMethod") as string | undefined;
      return <div>{paymentMethod || "—"}</div>;
    },
  },
  {
    id: "actions",
    cell: ({ row }) => {
      const expense = row.original;

      return (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="h-8 w-8 p-0">
              <span className="sr-only">Open menu</span>
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuLabel>Actions</DropdownMenuLabel>
            <DropdownMenuItem
              onClick={() => navigator.clipboard.writeText(expense.id)}
            >
              Copy ID
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem>View details</DropdownMenuItem>
            <DropdownMenuItem>Edit expense</DropdownMenuItem>
            {expense.status === 'draft' && (
              <DropdownMenuItem>Submit for approval</DropdownMenuItem>
            )}
            {expense.status === 'pending_approval' && (
              <>
                <DropdownMenuItem>Approve</DropdownMenuItem>
                <DropdownMenuItem>Reject</DropdownMenuItem>
              </>
            )}
            {expense.status === 'approved' && (
              <DropdownMenuItem>Mark as paid</DropdownMenuItem>
            )}
            {['draft', 'pending_approval'].includes(expense.status) && (
              <DropdownMenuItem className="text-destructive">Cancel</DropdownMenuItem>
            )}
          </DropdownMenuContent>
        </DropdownMenu>
      );
    },
  },
];

// Sample data for the expense table (using Partial to avoid required field errors)
const sampleExpenses: Partial<Expense>[] = [
  {
    id: "EXP-2025-0001",
    date: new Date("2025-01-15"),
    category: "Office Supplies",
    subcategory: "Stationery",
    amount: 125000,
    reference: "INV-2025-001",
    description: "Office supplies for headquarters",
    fiscalYear: "2025-2026",
    status: "paid",
    paymentMethod: "Bank Transfer",
  },
  {
    id: "EXP-2025-0002",
    date: new Date("2025-01-20"),
    category: "Travel and Transportation",
    subcategory: "Fuel",
    amount: 75000,
    reference: "FUEL-2025-001",
    description: "Fuel for official vehicles",
    status: "approved",
    paymentMethod: "Credit Card",
  },
  {
    id: "EXP-2025-0003",
    date: new Date("2025-01-25"),
    category: "Professional Services",
    amount: 500000,
    reference: "CONS-2025-001",
    description: "Consulting services for IT infrastructure",
    status: "pending_approval",
    paymentMethod: "Bank Transfer",
  },
  {
    id: "EXP-2025-0004",
    date: new Date("2025-01-28"),
    category: "Utilities",
    subcategory: "Electricity",
    amount: 350000,
    reference: "UTIL-2025-001",
    description: "Electricity bill for January 2025",
    status: "draft",
    paymentMethod: "Bank Transfer",
  },
  {
    id: "EXP-2025-0005",
    date: new Date("2025-02-01"),
    category: "Rent",
    amount: 1200000,
    reference: "RENT-2025-002",
    description: "Office rent for February 2025",
    status: "paid",
    paymentMethod: "Bank Transfer",
  },
  {
    id: "EXP-2025-0006",
    date: new Date("2025-02-05"),
    category: "Professional Development",
    subcategory: "Training",
    amount: 450000,
    reference: "TRN-2025-001",
    description: "Staff training on new certification procedures",
    status: "approved",
    paymentMethod: "Bank Transfer",
  },
  {
    id: "EXP-2025-0007",
    date: new Date("2025-02-10"),
    category: "Marketing",
    subcategory: "Advertising",
    amount: 250000,
    reference: "MKT-2025-001",
    description: "Newspaper advertisements for teacher certification",
    status: "rejected",
    paymentMethod: "Credit Card",
  },
  {
    id: "EXP-2025-0008",
    date: new Date("2025-02-15"),
    category: "Equipment",
    subcategory: "Computers",
    amount: 1800000,
    reference: "EQP-2025-001",
    description: "New computers for data processing department",
    status: "pending_approval",
    paymentMethod: "Bank Transfer",
  },
  {
    id: "EXP-2025-0009",
    date: new Date("2025-02-20"),
    category: "Maintenance",
    subcategory: "Building",
    amount: 350000,
    reference: "MNT-2025-001",
    description: "Building maintenance and repairs",
    status: "draft",
    paymentMethod: "Cash",
  },
  {
    id: "EXP-2025-0010",
    date: new Date("2025-02-25"),
    category: "Insurance",
    amount: 650000,
    reference: "INS-2025-001",
    description: "Annual insurance premium for office assets",
    status: "cancelled",
    paymentMethod: "Bank Transfer",
  },
];

interface ExpenseTableProps {
  title?: string;
  description?: string;
  showFilters?: boolean;
  fiscalYear?: string;
  budgetId?: string;
  limit?: number;
  onEditExpense?: (expense: Expense) => void;
  onDeleteExpense?: (expense: Expense) => void;
  onBulkDelete?: (expenseIds: string[]) => void;
  onBulkStatusUpdate?: (expenseIds: string[], status: string) => void;
  showBulkActions?: boolean;
}

export function ExpenseTable({
  title = "Expense Transactions",
  description = "View and manage all expense transactions for the Teachers Council of Malawi.",
  showFilters = true,
  fiscalYear,
  budgetId,
  limit = 10
}: ExpenseTableProps) {
  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});
  const [rowSelection, setRowSelection] = useState({});
  const [page, setPage] = useState(1);
  const [selectedBudget, setSelectedBudget] = useState<string | undefined>(budgetId);
  const [selectedFiscalYear, setSelectedFiscalYear] = useState<string | undefined>(fiscalYear);
  const [selectedStatus, setSelectedStatus] = useState<string | undefined>();

  // Get expense data using the custom hook
  const { useExpenseList } = useExpense();
  const { data, isLoading, error } = useExpenseList(page, limit, {
    fiscalYear: selectedFiscalYear,
    budget: selectedBudget,
    status: selectedStatus
  });

  // Get budgets for filtering
  const { activeBudgets, isLoadingActiveBudgets } = useBudget();

  // Get fiscal years for filtering from enhanced income store
  const { getActiveFiscalYears, getCurrentFiscalYear } = useIncomeStore();
  const fiscalYears = getActiveFiscalYears().length > 0
    ? getActiveFiscalYears().map(fy => fy.year)
    : [getCurrentFiscalYear()]; // Fallback to current fiscal year

  // Create table instance
  const table = useReactTable({
    data: data?.expenses || [],
    columns,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection,
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      rowSelection,
    },
  });

  return (
    <Card>
      <CardHeader>
        <CardTitle>{title}</CardTitle>
        <CardDescription>{description}</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="flex flex-col space-y-4">
          {showFilters && (
            <div className="flex flex-col space-y-2 sm:flex-row sm:items-center sm:justify-between sm:space-y-0 sm:space-x-2">
              <div className="flex flex-col space-y-2 sm:flex-row sm:items-center sm:space-y-0 sm:space-x-2">
                <Input
                  placeholder="Filter by reference..."
                  value={(table.getColumn("reference")?.getFilterValue() as string) ?? ""}
                  onChange={(event) =>
                    table.getColumn("reference")?.setFilterValue(event.target.value)
                  }
                  className="max-w-sm"
                />

                <Select
                  value={selectedFiscalYear}
                  onValueChange={setSelectedFiscalYear}
                >
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="Fiscal Year" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">All Fiscal Years</SelectItem>
                    {fiscalYears.map((year) => (
                      <SelectItem key={year} value={year}>{year}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                <Select
                  value={selectedBudget}
                  onValueChange={setSelectedBudget}
                >
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="Budget" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">All Budgets</SelectItem>
                    {activeBudgets?.map((budget: any) => (
                      <SelectItem key={budget.id} value={budget.id}>{budget.name}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                <Select
                  value={selectedStatus}
                  onValueChange={setSelectedStatus}
                >
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="Status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">All Statuses</SelectItem>
                    <SelectItem value="draft">Draft</SelectItem>
                    <SelectItem value="pending_approval">Pending Approval</SelectItem>
                    <SelectItem value="approved">Approved</SelectItem>
                    <SelectItem value="rejected">Rejected</SelectItem>
                    <SelectItem value="paid">Paid</SelectItem>
                    <SelectItem value="cancelled">Cancelled</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="flex items-center space-x-2">
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="outline" size="sm" className="h-9 gap-1">
                      <ChevronDown className="h-4 w-4" />
                      <span>Columns</span>
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    {table
                      .getAllColumns()
                      .filter((column) => column.getCanHide())
                      .map((column) => {
                        return (
                          <DropdownMenuCheckboxItem
                            key={column.id}
                            className="capitalize"
                            checked={column.getIsVisible()}
                            onCheckedChange={(value) =>
                              column.toggleVisibility(!!value)
                            }
                          >
                            {column.id}
                          </DropdownMenuCheckboxItem>
                        );
                      })}
                  </DropdownMenuContent>
                </DropdownMenu>
                <Button variant="outline" size="sm" className="h-9 gap-1">
                  <Download className="h-4 w-4" />
                  <span className="hidden sm:inline">Export</span>
                </Button>
              </div>
            </div>
          )}

          {isLoading ? (
            <div className="space-y-2">
              {Array(5).fill(0).map((_, i) => (
                <Skeleton key={i} className="h-12 w-full" />
              ))}
            </div>
          ) : error ? (
            <div className="py-6 text-center text-muted-foreground">
              <p>Error loading expense data. Please try again.</p>
            </div>
          ) : (
            <>
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    {table.getHeaderGroups().map((headerGroup) => (
                      <TableRow key={headerGroup.id}>
                        {headerGroup.headers.map((header) => {
                          return (
                            <TableHead key={header.id}>
                              {header.isPlaceholder
                                ? null
                                : flexRender(
                                    header.column.columnDef.header,
                                    header.getContext()
                                  )}
                            </TableHead>
                          );
                        })}
                      </TableRow>
                    ))}
                  </TableHeader>
                  <TableBody>
                    {table.getRowModel().rows?.length ? (
                      table.getRowModel().rows.map((row) => (
                        <TableRow
                          key={row.id}
                          data-state={row.getIsSelected() && "selected"}
                        >
                          {row.getVisibleCells().map((cell) => (
                            <TableCell key={cell.id}>
                              {flexRender(
                                cell.column.columnDef.cell,
                                cell.getContext()
                              )}
                            </TableCell>
                          ))}
                        </TableRow>
                      ))
                    ) : (
                      <TableRow>
                        <TableCell
                          colSpan={columns.length}
                          className="h-24 text-center"
                        >
                          No expenses found.
                        </TableCell>
                      </TableRow>
                    )}
                  </TableBody>
                </Table>
              </div>

              <div className="flex items-center justify-between space-x-2">
                <div className="text-sm text-muted-foreground">
                  Showing {table.getFilteredRowModel().rows.length} of {data?.pagination?.totalCount || 0} expenses
                </div>
                <div className="flex items-center space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setPage(page > 1 ? page - 1 : 1)}
                    disabled={page === 1}
                  >
                    Previous
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setPage(page + 1)}
                    disabled={!data?.pagination?.hasNextPage}
                  >
                    Next
                  </Button>
                </div>
              </div>
            </>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
